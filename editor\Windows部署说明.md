# DL引擎编辑器 - Windows Docker Desktop 部署说明

## 📋 概述

本文档详细说明如何在Windows环境下使用Docker Desktop部署DL引擎编辑器。编辑器采用React + Nginx的架构，通过Docker容器化部署，提供高性能的3D场景编辑功能。

## 🔧 系统要求

### 硬件要求
- **CPU**: Intel i5 或 AMD Ryzen 5 以上
- **内存**: 8GB 以上（推荐16GB）
- **存储**: 10GB 可用空间
- **显卡**: 支持WebGL 2.0的独立显卡（推荐）

### 软件要求
- **操作系统**: Windows 10 版本2004 或 Windows 11
- **Docker Desktop**: 4.0.0 或更高版本
- **WSL2**: 已启用并配置
- **浏览器**: Chrome 90+, Firefox 88+, Edge 90+

## 🚀 部署步骤

### 1. 环境准备

#### 安装Docker Desktop
```powershell
# 1. 下载Docker Desktop for Windows
# 访问: https://www.docker.com/products/docker-desktop

# 2. 启用WSL2
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

# 3. 重启计算机后设置WSL2为默认版本
wsl --set-default-version 2
```

#### 验证Docker安装
```powershell
# 检查Docker版本
docker --version
docker-compose --version

# 检查Docker服务状态
docker info
```

### 2. 项目部署

#### 克隆项目（如果需要）
```powershell
git clone <repository-url>
cd newsystem
```

#### 构建编辑器镜像
```powershell
# 进入编辑器目录
cd editor

# 构建Docker镜像
docker build -t dl-engine-editor:latest .

# 验证镜像构建成功
docker images | findstr dl-engine-editor
```

#### 使用Docker Compose部署
```powershell
# 返回项目根目录
cd ..

# 启动完整系统（包括编辑器）
docker-compose up -d

# 或者只启动编辑器相关服务
docker-compose up -d mysql redis service-registry api-gateway dl-engine-editor
```1

### 3. 验证部署

#### 检查容器状态
```powershell
# 查看所有容器状态
docker-compose ps

# 查看编辑器容器日志
docker-compose logs -f editor

# 检查编辑器健康状态
curl http://localhost/health
```

#### 访问编辑器
- **编辑器地址**: http://localhost
- **API文档**: http://localhost:3000/api/docs
- **健康检查**: http://localhost/health

## 📁 目录结构说明

```
editor/
├── Dockerfile              # Docker构建文件（已优化）
├── nginx.conf              # Nginx配置文件（已优化）
├── dist/                   # 构建产物目录
│   ├── index.html          # 主页面
│   └── assets/             # 静态资源
├── src/                    # 源代码目录
├── package.json            # 项目依赖
└── Windows部署说明.md       # 本文档
```

## ⚙️ 配置说明

### Nginx配置特性
- **Gzip压缩**: 减少传输大小，提高加载速度
- **静态资源缓存**: 1年缓存期，提高性能
- **API代理**: 自动代理到后端API网关
- **WebSocket支持**: 支持实时协作功能
- **SPA路由**: 支持前端路由，刷新页面不会404
- **安全头**: 添加安全相关的HTTP头
- **健康检查**: 提供/health端点用于监控

### 环境变量
```yaml
environment:
  - REACT_APP_API_URL=http://localhost:3000/api
  - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
```

## 🔍 故障排查

### 常见问题

#### 1. 容器启动失败
```powershell
# 查看详细错误日志
docker-compose logs editor

# 检查端口占用
netstat -ano | findstr :80

# 重新构建镜像
docker-compose build --no-cache editor
```

#### 2. 页面无法访问
```powershell
# 检查容器是否运行
docker ps | findstr editor

# 检查网络连接
docker network ls
docker network inspect dl-engine-network
```

#### 3. API请求失败
```powershell
# 检查API网关状态
curl http://localhost:3000/api/health

# 检查容器间网络通信
docker exec dl-engine-editor ping api-gateway
```

#### 4. 性能问题
```powershell
# 检查容器资源使用
docker stats

# 增加Docker Desktop内存分配
# Docker Desktop -> Settings -> Resources -> Advanced
```

### 日志查看
```powershell
# 实时查看编辑器日志
docker-compose logs -f editor

# 查看nginx访问日志
docker exec dl-engine-editor tail -f /var/log/nginx/access.log

# 查看nginx错误日志
docker exec dl-engine-editor tail -f /var/log/nginx/error.log
```

## 🔧 性能优化

### Docker Desktop优化
1. **内存分配**: 建议分配至少4GB内存给Docker
2. **CPU核心**: 分配至少2个CPU核心
3. **磁盘空间**: 确保有足够的磁盘空间用于镜像和容器

### 浏览器优化
1. **启用硬件加速**: 确保浏览器启用GPU硬件加速
2. **清除缓存**: 定期清除浏览器缓存
3. **关闭不必要的扩展**: 减少浏览器扩展的影响

## 📚 相关文档

- [系统部署文档](../系统部署2025-7-14.md)
- [Docker Compose配置](../docker-compose.yml)
- [API网关文档](../server/api-gateway/README.md)

## 🆘 技术支持

如遇到问题，请：
1. 查看本文档的故障排查部分
2. 检查Docker Desktop和WSL2配置
3. 查看容器日志获取详细错误信息
4. 联系技术支持团队

---

**注意**: 本部署方案已针对Windows Docker Desktop环境进行优化，确保在Windows系统上的最佳性能和稳定性。
