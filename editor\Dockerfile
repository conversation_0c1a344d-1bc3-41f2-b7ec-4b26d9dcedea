# ================================
# 多阶段构建 - 构建阶段
# ================================
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（针对中国用户优化）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件并安装依赖
COPY package*.json ./
RUN npm ci --silent

# 清理可能存在的构建缓存
RUN rm -rf dist/ build/ node_modules/.cache/

# 复制源代码（.dockerignore会排除node_modules等）
COPY . .

# 确保没有权限问题
RUN chmod -R 755 /app

# 构建应用
RUN npm run build

# 验证构建结果
RUN ls -la dist/

# ================================
# 生产环境 - Nginx阶段
# ================================
FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache curl

# 创建nginx用户和组（安全性）
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S nginx-app -u 1001

# 复制构建产物到Nginx服务目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制优化的Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 设置正确的文件权限
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# 创建nginx运行所需的目录
RUN mkdir -p /var/cache/nginx/client_temp && \
    mkdir -p /var/cache/nginx/proxy_temp && \
    mkdir -p /var/cache/nginx/fastcgi_temp && \
    mkdir -p /var/cache/nginx/uwsgi_temp && \
    mkdir -p /var/cache/nginx/scgi_temp && \
    chown -R nginx-app:nginx-app /var/cache/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 使用非root用户运行（安全性）
USER nginx-app

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
