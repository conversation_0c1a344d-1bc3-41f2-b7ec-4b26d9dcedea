@echo off
echo ================================
echo DL引擎编辑器 Docker构建脚本
echo ================================

echo.
echo 1. 清理旧的Docker镜像和容器...
docker stop dl-engine-editor 2>nul
docker rm dl-engine-editor 2>nul
docker rmi dl-engine-editor:latest 2>nul

echo.
echo 2. 清理Docker构建缓存...
docker builder prune -f

echo.
echo 3. 检查.dockerignore文件...
if exist .dockerignore (
    echo ✓ .dockerignore文件存在
    echo 内容预览:
    type .dockerignore | findstr /n "node_modules dist"
) else (
    echo ✗ .dockerignore文件不存在，正在创建...
    echo node_modules/ > .dockerignore
    echo dist/ >> .dockerignore
    echo coverage/ >> .dockerignore
)

echo.
echo 4. 检查必要文件...
if exist package.json (
    echo ✓ package.json存在
) else (
    echo ✗ package.json不存在
    exit /b 1
)

if exist src\ (
    echo ✓ src目录存在
) else (
    echo ✗ src目录不存在
    exit /b 1
)

if exist vite.config.ts (
    echo ✓ vite.config.ts存在
) else (
    echo ✗ vite.config.ts不存在
    exit /b 1
)

echo.
echo 5. 开始构建Docker镜像...
echo 构建命令: docker build -t dl-engine-editor:latest .
echo.

docker build -t dl-engine-editor:latest . --progress=plain --no-cache

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================
    echo ✓ Docker镜像构建成功！
    echo ================================
    echo.
    echo 6. 验证镜像...
    docker images | findstr dl-engine-editor
    echo.
    echo 7. 可以使用以下命令运行容器:
    echo docker run -d -p 80:80 --name dl-engine-editor dl-engine-editor:latest
    echo.
    echo 或者使用docker-compose:
    echo docker-compose up -d editor
) else (
    echo.
    echo ================================
    echo ✗ Docker镜像构建失败！
    echo ================================
    echo.
    echo 故障排查建议:
    echo 1. 检查网络连接
    echo 2. 确保Docker Desktop正在运行
    echo 3. 检查磁盘空间是否充足
    echo 4. 查看上面的错误信息
    echo.
    echo 如需帮助，请查看Windows部署说明.md文档
)

pause
